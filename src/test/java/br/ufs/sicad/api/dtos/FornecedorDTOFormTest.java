package br.ufs.sicad.api.dtos;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class FornecedorDTOFormTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testDeserializationWithStringFields() throws Exception {
        // JSON com campos como strings simples (como vem do frontend)
        String json = """
            {
                "cnpj": "12345678901234",
                "razaoSocial": "Empresa Teste",
                "email": "<EMAIL>",
                "telefone": "(79) 9999-9999"
            }
            """;

        FornecedorDTOForm form = objectMapper.readValue(json, FornecedorDTOForm.class);

        assertNotNull(form);
        assertEquals("12345678901234", form.cnpj());
        assertEquals("Empresa Teste", form.razaoSocial());
        assertEquals("<EMAIL>", form.email);
        assertEquals("(79) 9999-9999", form.telefone);

        // Verifica se os métodos auxiliares funcionam
        assertEquals(1, form.email().size());
        assertEquals("<EMAIL>", form.email().get(0));
        assertEquals(1, form.telefone().size());
        assertEquals("(79) 9999-9999", form.telefone().get(0));
    }

    @Test
    void testDeserializationWithNullFields() throws Exception {
        // JSON com campos nulos
        String json = """
            {
                "cnpj": "12345678901234",
                "razaoSocial": "Empresa Teste",
                "email": null,
                "telefone": null
            }
            """;

        FornecedorDTOForm form = objectMapper.readValue(json, FornecedorDTOForm.class);

        assertNotNull(form);
        assertEquals("12345678901234", form.cnpj());
        assertEquals("Empresa Teste", form.razaoSocial());
        assertNull(form.email);
        assertNull(form.telefone);

        assertTrue(form.email().isEmpty());
        assertTrue(form.telefone().isEmpty());
    }

    @Test
    void testDeserializationWithEmptyStringFields() throws Exception {
        // JSON com campos vazios
        String json = """
            {
                "cnpj": "12345678901234",
                "razaoSocial": "Empresa Teste",
                "email": "",
                "telefone": ""
            }
            """;

        FornecedorDTOForm form = objectMapper.readValue(json, FornecedorDTOForm.class);

        assertNotNull(form);
        assertEquals("12345678901234", form.cnpj());
        assertEquals("Empresa Teste", form.razaoSocial());
        assertEquals("", form.email);
        assertEquals("", form.telefone);

        assertTrue(form.email().isEmpty());
        assertTrue(form.telefone().isEmpty());
    }

    @Test
    void testAsFornecedorMethod() throws Exception {
        String json = """
            {
                "cnpj": "12345678901234",
                "razaoSocial": "Empresa Teste",
                "email": "<EMAIL>",
                "telefone": "(79) 9999-9999"
            }
            """;

        FornecedorDTOForm form = objectMapper.readValue(json, FornecedorDTOForm.class);
        var fornecedor = form.asFornecedor();

        assertNotNull(fornecedor);
        assertEquals("12345678901234", fornecedor.getCnpj());
        assertEquals("Empresa Teste", fornecedor.getRazaoSocial());
        assertEquals(1, fornecedor.getEmail().size());
        assertEquals("<EMAIL>", fornecedor.getEmail().get(0));
        assertEquals(1, fornecedor.getTelefone().size());
        assertEquals("(79) 9999-9999", fornecedor.getTelefone().get(0));
    }
}
