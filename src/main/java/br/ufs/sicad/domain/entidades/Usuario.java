package br.ufs.sicad.domain.entidades;

import java.util.HashSet;
import java.util.Set;

import br.ufs.sicad.domain.entidades.enums.Status;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "usuario")
@Entity
public class Usuario {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "nome", nullable = false, length = 255)
    private String nome;

    @Column(name = "sobrenome")
    private String sobrenome;

    @Column(name = "matricula", length = 50, unique = true)
    private String matricula;

    @Column(name = "cpf", length = 11, unique = true, nullable = false)
    private String cpf;

    @Column(name = "email", length = 255, unique = true, nullable = false)
    private String email;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 7)
    private Status status;

    @Column(name = "telefone", length = 20)
    private String telefone;

    @Column(name = "senha", length = 255)
    private String senha;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "usuario_perfil_usuario",
            joinColumns = @JoinColumn(name = "usuario_id"),
            inverseJoinColumns = @JoinColumn(name = "perfil_usuario_id")
    )
    private Set<PerfilUsuario> perfis = new HashSet<>();

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "usuario_unidade_organizacional",
            joinColumns = @JoinColumn(name = "usuario_id"),
            inverseJoinColumns = @JoinColumn(name = "unidade_organizacional_id")
    )
    private Set<UnidadeOrganizacional> unidadesOrganizacionais = new HashSet<>();

    public Usuario(String nome, String sobrenome, String matriculaFuncional, String cpf, String email, String telefone, String senha){
        this.nome = nome;
        this.sobrenome = sobrenome;
        this.matricula = matriculaFuncional;
        this.cpf = cpf;
        this.email = email;
        this.status = Status.ATIVO;
        this.telefone = telefone;
        this.senha = senha;
    }

    public void inativar(){
        this.status = Status.INATIVO;
    }

    public void ativar(){
        this.status = Status.ATIVO;
    }
}
