package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.entidades.enums.Status;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Table(name = "unidade_organizacional")
@Getter
@Setter
@Entity
public class UnidadeOrganizacional {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "O nome é obrigatório")
    @Size(max = 100, message = "O nome deve ter no máximo 100 caracteres")
    private String nome;

    @NotBlank(message = "A sigla é obrigatória")
    @Size(max = 10, message = "A sigla deve ter no máximo 10 caracteres")
    private String sigla;

    @ManyToOne
    @JoinColumn(name = "unidade_pai_id")
    private UnidadeOrganizacional unidadePai;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private Status status = Status.ATIVO;
}