package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.AtualizaUsuarioDTOForm;
import br.ufs.sicad.api.dtos.UsuarioDTO;
import br.ufs.sicad.api.dtos.UsuarioDTOForm;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.services.UsuarioService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("usuarios")
public class UsuarioController {

    private final UsuarioService usuarioService;

    public UsuarioController(UsuarioService usuarioService) {
        this.usuarioService = usuarioService;
    }

    @GetMapping
    public ResponseEntity<Page<UsuarioDTO>> listarUsuarios(
            @RequestParam(required = false) String nome,
            @RequestParam(required = false) String matricula,
            @RequestParam(required = false) String cpf,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String telefone,
            @RequestParam(required = false) Long unidadeOrganizacionalId,
            @RequestParam(required = false) Long perfilId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);

        Page<Usuario> usuarios = usuarioService.listarUsuarios(nome, matricula, cpf, email, telefone,
                unidadeOrganizacionalId, perfilId, pageable);

        List<UsuarioDTO> usuariosDTO = usuarios.getContent().stream()
                .map(UsuarioDTO::from)
                .collect(Collectors.toList());

        Page<UsuarioDTO> usuariosPage = new PageImpl<>(usuariosDTO, pageable, usuarios.getTotalElements());

        return ResponseEntity.status(HttpStatus.OK).body(usuariosPage);
    }

    @GetMapping("/info")
    public ResponseEntity<UsuarioDTO> UsuarioInfo(@AuthenticationPrincipal Jwt jwt) {
        String email = jwt.getSubject();
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioService.buscarUsuarioPor(email)));
    }

    @PostMapping
    public ResponseEntity<UsuarioDTO> criarUsuario(@RequestBody UsuarioDTOForm usuarioDTOForm) {
        Usuario usuarioCriado = usuarioService.criarUsuario(usuarioDTOForm.asUsuario(), usuarioDTOForm.perfisIds());
        return ResponseEntity.status(HttpStatus.CREATED).body(UsuarioDTO.from(usuarioCriado));
    }

    @GetMapping("{id}")
    public ResponseEntity<UsuarioDTO> buscarUsuario(@PathVariable Long id) {
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioService.buscarUsuarioPor(id)));
    }

    @PutMapping("{id}")
    public ResponseEntity<UsuarioDTO> atualizarUsuario(@PathVariable Long id, @RequestBody AtualizaUsuarioDTOForm atualizaUsuarioDTOForm) {
        Usuario usuarioAtualizado = usuarioService.atualizarUsuario(
                id,
                atualizaUsuarioDTOForm.matriculaFuncional(),
                atualizaUsuarioDTOForm.nome(),
                atualizaUsuarioDTOForm.email(),
                atualizaUsuarioDTOForm.cpf(),
                atualizaUsuarioDTOForm.telefone(),
                atualizaUsuarioDTOForm.status(),
                atualizaUsuarioDTOForm.senha()
        );
        return ResponseEntity.status(HttpStatus.OK).body(UsuarioDTO.from(usuarioAtualizado));
    }

    @DeleteMapping("{id}")
    public ResponseEntity<Void> deletarUsuario(@PathVariable Long id) {
        usuarioService.deletarUsuario(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
