package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.FornecedorDTO;
import br.ufs.sicad.api.dtos.FornecedorDTOForm;
import br.ufs.sicad.domain.entidades.Fornecedor;
import br.ufs.sicad.services.FornecedorService;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/fornecedores")
public class FornecedorController {

    private final FornecedorService fornecedorService;

    public FornecedorController(FornecedorService fornecedorService) {
        this.fornecedorService = fornecedorService;
    }

    @GetMapping
    public ResponseEntity<Page<FornecedorDTO>> listarFornecedores(
            @RequestParam(required = false) String cnpj,
            @RequestParam(required = false) String razaoSocial,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PageRequest.of(page, size);
        Page<Fornecedor> fornecedores = fornecedorService.listarFornecedores(cnpj, razaoSocial, pageable);

        List<FornecedorDTO> fornecedoresDTO = fornecedores.getContent().stream()
                .map(FornecedorDTO::from)
                .collect(Collectors.toList());

        Page<FornecedorDTO> fornecedoresPage = new PageImpl<>(fornecedoresDTO, pageable, fornecedores.getTotalElements());

        return ResponseEntity.ok(fornecedoresPage);
    }

    @PostMapping
    public ResponseEntity<FornecedorDTO> criarFornecedor(@RequestBody @Valid FornecedorDTOForm fornecedorForm) {
        Fornecedor fornecedor = fornecedorService.criarFornecedor(fornecedorForm.asFornecedor());
        return ResponseEntity.status(HttpStatus.CREATED).body(FornecedorDTO.from(fornecedor));
    }

    @GetMapping("/{id}")
    public ResponseEntity<FornecedorDTO> buscarFornecedor(@PathVariable Long id) {
        Fornecedor fornecedor = fornecedorService.buscarFornecedorPor(id);
        return ResponseEntity.ok(FornecedorDTO.from(fornecedor));
    }

    @PutMapping("/{id}")
    public ResponseEntity<FornecedorDTO> atualizarFornecedor(
            @PathVariable Long id,
            @RequestBody @Valid FornecedorDTOForm fornecedorForm) {
        
        Fornecedor fornecedor = fornecedorService.atualizarFornecedor(
                id,
                fornecedorForm.cnpj(),
                fornecedorForm.razaoSocial(),
                fornecedorForm.getEmailList(),
                fornecedorForm.getTelefoneList()
        );
        return ResponseEntity.ok(FornecedorDTO.from(fornecedor));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletarFornecedor(@PathVariable Long id) {
        fornecedorService.deletarFornecedor(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/todos")
    public ResponseEntity<List<FornecedorDTO>> listarTodosFornecedores() {
        List<Fornecedor> fornecedores = fornecedorService.listarTodosFornecedores();
        List<FornecedorDTO> fornecedoresDTO = fornecedores.stream()
                .map(FornecedorDTO::from)
                .collect(Collectors.toList());
        return ResponseEntity.ok(fornecedoresDTO);
    }
}