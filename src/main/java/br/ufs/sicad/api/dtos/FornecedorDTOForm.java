package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Fornecedor;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

public record FornecedorDTOForm(
        @NotBlank(message = "CNPJ é obrigatório")
        String cnpj,
        
        @NotBlank(message = "Razão social é obrigatória")
        String razaoSocial,
        
        List<String> email,
        List<String> telefone
) {
    public Fornecedor asFornecedor() {
        Fornecedor fornecedor = new Fornecedor(this.cnpj, this.razaoSocial);
        if (this.email != null) {
            fornecedor.setEmail(this.email);
        }
        if (this.telefone != null) {
            fornecedor.setTelefone(this.telefone);
        }
        return fornecedor;
    }
}