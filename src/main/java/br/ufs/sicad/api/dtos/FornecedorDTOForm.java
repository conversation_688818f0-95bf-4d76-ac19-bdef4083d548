package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Fornecedor;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;

import java.util.ArrayList;
import java.util.List;

public record FornecedorDTOForm(
        @NotBlank(message = "CNPJ é obrigatório")
        String cnpj,

        @NotBlank(message = "Razão social é obrigatória")
        String razaoSocial,

        @JsonProperty("email")
        JsonNode emailNode,

        @JsonProperty("telefone")
        JsonNode telefoneNode
) {
    public Fornecedor asFornecedor() {
        Fornecedor fornecedor = new Fornecedor(this.cnpj, this.razaoSocial);

        // Processar email
        List<String> emailList = processJsonNodeToList(this.emailNode);
        if (emailList != null && !emailList.isEmpty()) {
            fornecedor.setEmail(emailList);
        }

        // Processar telefone
        List<String> telefoneList = processJsonNodeToList(this.telefoneNode);
        if (telefoneList != null && !telefoneList.isEmpty()) {
            fornecedor.setTelefone(telefoneList);
        }

        return fornecedor;
    }

    private List<String> processJsonNodeToList(JsonNode node) {
        if (node == null || node.isNull()) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();

        if (node.isTextual()) {
            // Se for uma string simples, adiciona à lista
            String value = node.asText().trim();
            if (!value.isEmpty()) {
                result.add(value);
            }
        } else if (node.isArray()) {
            // Se for um array, processa cada elemento
            for (JsonNode element : node) {
                if (element.isTextual()) {
                    String value = element.asText().trim();
                    if (!value.isEmpty()) {
                        result.add(value);
                    }
                }
            }
        }

        return result;
    }

    // Métodos auxiliares para compatibilidade
    public List<String> email() {
        return processJsonNodeToList(this.emailNode);
    }

    public List<String> telefone() {
        return processJsonNodeToList(this.telefoneNode);
    }
}