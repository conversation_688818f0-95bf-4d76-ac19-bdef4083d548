package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Fornecedor;
import jakarta.validation.constraints.NotBlank;

import java.util.ArrayList;
import java.util.List;

public record FornecedorDTOForm(
        @NotBlank(message = "CNPJ é obrigatório")
        String cnpj,

        @NotBlank(message = "Razão social é obrigatória")
        String razaoSocial,

        String email,
        String telefone
) {
    public Fornecedor asFornecedor() {
        Fornecedor fornecedor = new Fornecedor(this.cnpj, this.razaoSocial);

        // Processar email
        if (this.email != null && !this.email.trim().isEmpty()) {
            List<String> emailList = new ArrayList<>();
            emailList.add(this.email.trim());
            fornecedor.setEmail(emailList);
        }

        // Processar telefone
        if (this.telefone != null && !this.telefone.trim().isEmpty()) {
            List<String> telefoneList = new ArrayList<>();
            telefoneList.add(this.telefone.trim());
            fornecedor.setTelefone(telefoneList);
        }

        return fornecedor;
    }

    // Métodos auxiliares para compatibilidade com o service
    public List<String> getEmailList() {
        if (this.email != null && !this.email.trim().isEmpty()) {
            List<String> emailList = new ArrayList<>();
            emailList.add(this.email.trim());
            return emailList;
        }
        return new ArrayList<>();
    }

    public List<String> getTelefoneList() {
        if (this.telefone != null && !this.telefone.trim().isEmpty()) {
            List<String> telefoneList = new ArrayList<>();
            telefoneList.add(this.telefone.trim());
            return telefoneList;
        }
        return new ArrayList<>();
    }
}