package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Usuario;

import java.util.Set;
import java.util.stream.Collectors;

public record UsuarioDTO (
        Long id,
        String nome,
        String sobrenome,
        String matricula,
        String cpf,
        String email,
        String status,
        String telefone,
        Set<PerfilUsuarioDTO> perfis,
        Set<UnidadeOrganizacionalDTO> unidadesOrganizacionais

){
    public static UsuarioDTO from(Usuario usuario){
        return new UsuarioDTO(
                usuario.getId(),
                usuario.getNome(),
                usuario.getSobrenome(),
                usuario.getMatricula(),
                usuario.getCpf(),
                usuario.getEmail(),
                usuario.getStatus().name(),
                usuario.getTelefone(),
                usuario.getPerfis()
                        .stream()
                        .map(PerfilUsuarioDTO::from)
                        .collect(Collectors.toSet()),
                usuario.getUnidadesOrganizacionais()
                        .stream()
                        .map(UnidadeOrganizacionalDTO::from)
                        .collect(Collectors.toSet()));
    }
}

