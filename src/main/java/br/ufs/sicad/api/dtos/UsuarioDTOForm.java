package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Usuario;

import java.util.Set;


public record UsuarioDTOForm (
        String nome,
        String matriculaFuncional,
        String cpf,
        String email,
        String status,
        String telefone,
        String senha,
        Set<Long> perfisIds,
        Set<Long> unidades
){
    public Usuario asUsuario(){
        return new Usuario(
                this.nome,
                this.matriculaFuncional,
                this.cpf,
                this.email,
                this.status,
                this.telefone,
                this.senha
        );
    }
}
