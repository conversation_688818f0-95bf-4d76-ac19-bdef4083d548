package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Usuario;

import java.util.Set;


public record UsuarioDTOForm (
        String nome,
        String sobrenome,
        String matricula,
        String cpf,
        String email,
        String telefone,
        String senha,
        Set<Long> perfisIds,
        Set<Long> unidades
){
    public Usuario asUsuario(){
        return new Usuario(
                this.nome,
                this.sobrenome,
                this.matricula,
                this.cpf,
                this.email,
                this.telefone,
                this.senha
        );
    }
}
