package br.ufs.sicad.services;

import br.ufs.sicad.api.dtos.UnidadeOrganizacionalDTO;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.HashSet;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class UnidadeOrganizacionalService {

    private final UnidadeOrganizacionalRepository repository;

    public Page<UnidadeOrganizacional> listarTodos(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public UnidadeOrganizacional buscarPorId(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Unidade Organizacional não encontrada"));
    }

    @Transactional
    public UnidadeOrganizacional criar(UnidadeOrganizacionalDTO dto) {
        if (repository.existsBySigla(dto.sigla())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Já existe uma unidade organizacional com esta sigla");
        }

        UnidadeOrganizacional unidade = new UnidadeOrganizacional();
        unidade.setNome(dto.nome());
        unidade.setSigla(dto.sigla());

        if (dto.unidadePaiId() != null) {
            UnidadeOrganizacional unidadePai = repository.findById(dto.unidadePaiId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unidade pai não encontrada"));

            if (isCircularReference(unidadePai, null)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "A hierarquia de unidades organizacionais não pode conter loops");
            }

            unidade.setUnidadePai(unidadePai);
        }

        return repository.save(unidade);
    }

    @Transactional
    public UnidadeOrganizacional atualizar(Long id, UnidadeOrganizacionalDTO dto) {
        UnidadeOrganizacional unidade = repository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Unidade Organizacional não encontrada"));

        if (!unidade.getSigla().equals(dto.sigla()) && repository.existsBySigla(dto.sigla())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Já existe uma unidade organizacional com esta sigla");
        }

        unidade.setNome(dto.nome());
        unidade.setSigla(dto.sigla());

        if (dto.unidadePaiId() != null) {
            if (dto.unidadePaiId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Uma unidade não pode ser pai de si mesma");
            }

            UnidadeOrganizacional unidadePai = repository.findById(dto.unidadePaiId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unidade pai não encontrada"));
            unidade.setUnidadePai(unidadePai);
        } else {
            unidade.setUnidadePai(null);
        }

        return repository.save(unidade);
    }

    @Transactional
    public void excluir(Long id) {
        if (!repository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Unidade Organizacional não encontrada");
        }

        if (repository.existsByUnidadePaiId(id)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Não é possível excluir uma unidade que é pai de outras unidades");
        }

        repository.deleteById(id);
    }

    private boolean isCircularReference(UnidadeOrganizacional unidade, Set<Long> visitedIds) {
        if (visitedIds == null) {
            visitedIds = new HashSet<>();
        }

        if (unidade == null) {
            return false;
        }

        if (visitedIds.contains(unidade.getId())) {
            return true;
        }

        visitedIds.add(unidade.getId());

        return isCircularReference(unidade.getUnidadePai(), visitedIds);
    }
}