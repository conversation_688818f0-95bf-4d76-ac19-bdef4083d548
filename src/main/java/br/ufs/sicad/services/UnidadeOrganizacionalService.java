package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.UniqueResourceViolationException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.entidades.enums.Status;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
public class UnidadeOrganizacionalService {

    private final UnidadeOrganizacionalRepository repository;

    public Page<UnidadeOrganizacional> listarTodos(Pageable pageable) {
        return repository.findByStatus(Status.ATIVO, pageable);
    }

    public Page<UnidadeOrganizacional> listarComFiltros(String nome, String sigla, Status status, Pageable pageable) {
        String nomeLower = nome != null ? nome.toLowerCase() : null;
        String siglaLower = sigla != null ? sigla.toLowerCase() : null;
        
        return repository.findByFilters(nomeLower, siglaLower, status, pageable);
    }

    public UnidadeOrganizacional buscarPorId(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada"));
    }

    @Transactional
    public UnidadeOrganizacional criar(String nome, String sigla, Long unidadePaiId) {
        if (repository.existsBySiglaAndStatus(sigla, Status.ATIVO)) {
            throw new UniqueResourceViolationException("Já existe uma unidade organizacional ativa com esta sigla");
        }

        UnidadeOrganizacional unidade = new UnidadeOrganizacional();
        unidade.setNome(nome);
        unidade.setSigla(sigla);
        unidade.setStatus(Status.ATIVO);

        if (unidadePaiId != null) {
            UnidadeOrganizacional unidadePai = repository.findById(unidadePaiId)
                    .orElseThrow(() -> new ResourceNotFoundException("Unidade pai não encontrada"));
            unidade.setUnidadePai(unidadePai);
        }

        return repository.save(unidade);
    }

    @Transactional
    public UnidadeOrganizacional atualizar(Long id, String nome, String sigla, Long unidadePaiId) {
        UnidadeOrganizacional unidade = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada"));

        if (!unidade.getSigla().equals(sigla) && repository.existsBySiglaAndStatus(sigla, Status.ATIVO)) {
            throw new ResourceNotFoundException("Já existe uma unidade organizacional ativa com esta sigla");
        }

        unidade.setNome(nome);
        unidade.setSigla(sigla);

        if (unidadePaiId != null) {
            if (unidadePaiId.equals(id)) {
                throw new ValidationException("Uma unidade não pode ser pai de si mesma");
            }

            UnidadeOrganizacional unidadePai = repository.findById(unidadePaiId)
                    .orElseThrow(() -> new ValidationException("Unidade pai não encontrada"));
            
            if (unidadePai.getUnidadePai() != null && unidadePai.getUnidadePai().getId().equals(id)) {
                throw new ValidationException("Não é possível criar referência circular na hierarquia");
            }
            
            unidade.setUnidadePai(unidadePai);
        } else {
            unidade.setUnidadePai(null);
        }

        return repository.save(unidade);
    }

    @Transactional
    public void inativar(Long id) {
        UnidadeOrganizacional unidade = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada"));

        if (repository.existsByUnidadePaiId(id)) {
            throw new ValidationException("Não é possível inativar uma unidade que é pai de outras unidades");
        }

        unidade.setStatus(Status.INATIVO);
        repository.save(unidade);
    }

    @Transactional
    public UnidadeOrganizacional alterarStatus(Long id, Status status) {
        UnidadeOrganizacional unidade = repository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Unidade Organizacional não encontrada"));

        if (status == Status.INATIVO && repository.existsByUnidadePaiId(id)) {
            throw new ValidationException("Não é possível inativar uma unidade que é pai de outras unidades");
        }

        unidade.setStatus(status);
        return repository.save(unidade);
    }
}