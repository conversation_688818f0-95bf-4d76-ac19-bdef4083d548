package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.UniqueResourceViolationException;
import br.ufs.sicad.domain.entidades.PerfilUsuario;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.infrastructure.repositories.PerfilUsuarioRepository;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service
public class UsuarioService {
    @Autowired
    private final UsuarioRepository usuarioRepository;
    @Autowired
    private final PerfilUsuarioRepository perfilUsuarioRepository;

    public UsuarioService(UsuarioRepository usuarioRepository, PerfilUsuarioRepository perfilUsuarioRepository) {
        this.usuarioRepository = usuarioRepository;
        this.perfilUsuarioRepository = perfilUsuarioRepository;
    }

    public Page<Usuario> listarUsuarios(String nome, String matricula, String cpf, String email, 
        String telefone, Long unidadeOrganizacionalId, Long perfilId, Pageable pageable) {
    return usuarioRepository.findByFilters(nome, matricula, cpf, email, telefone, 
            unidadeOrganizacionalId, perfilId, pageable);
}

    public Usuario buscarUsuarioPor(Long UsuarioId) {
        return usuarioRepository.findById(UsuarioId)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado. - Usuário ID: " + UsuarioId));
    }

    public Usuario buscarUsuarioPor(String email){
        return usuarioRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("Usuário não encontrado. - Email: " + email));
    }

    public Usuario criarUsuario(Usuario usuario, Set<Long> perfisUsuario) {
        if (usuarioRepository.existsByEmail(usuario.getEmail())){
            throw new UniqueResourceViolationException("Email já cadastrado.");
        }
        if (perfisUsuario != null) {
            Set<PerfilUsuario> novosPerfis = new HashSet<>();
            for (Long perfilId : perfisUsuario) {
                PerfilUsuario perfil = perfilUsuarioRepository.findById(perfilId)
                        .orElseThrow(() -> new ResourceNotFoundException("Perfil não encontrado: " + perfilId));
                novosPerfis.add(perfil);
            }
            usuario.setPerfis(novosPerfis);
        }
        return usuarioRepository.save(usuario);
    }

    public Usuario atualizarUsuario(Long id, String matriculaFuncional, String novoNome, String novoEmail, String novoCpf, String novoTelefone, String novoStatus, String senha) {
        Usuario usuario = buscarUsuarioPor(id);
        usuario.setMatriculaFuncional(matriculaFuncional);
        usuario.setNome(novoNome);
        usuario.setEmail(novoEmail);
        usuario.setCpf(novoCpf);
        usuario.setTelefone(novoTelefone);
        usuario.setStatus(novoStatus);
        usuario.setSenha(new BCryptPasswordEncoder().encode(senha));
        return usuarioRepository.save(usuario);
    }

    public void deletarUsuario(Long id) {
        buscarUsuarioPor(id);
        usuarioRepository.deleteById(id);
    }



}