package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.Usuario;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UsuarioRepository extends JpaRepository<Usuario, Long> {

    @Query("SELECT DISTINCT u FROM Usuario u " +
            "LEFT JOIN FETCH u.perfis p " +
            "LEFT JOIN FETCH u.unidadesOrganizacionais uo " +
            "WHERE " +
            "(:nome IS NULL OR u.nome LIKE %:nome%) AND " +
            "(:matricula IS NULL OR u.matriculaFuncional LIKE %:matricula%) AND " +
            "(:cpf IS NULL OR u.cpf LIKE %:cpf%) AND " +
            "(:email IS NULL OR u.email LIKE %:email%) AND " +
            "(:telefone IS NULL OR u.telefone LIKE %:telefone%) AND " +
            "(:unidadeOrganizacionalId IS NULL OR uo.id = :unidadeOrganizacionalId) AND " +
            "(:perfilId IS NULL OR p.id = :perfilId)")
    Page<Usuario> findByFilters(
            @Param("nome") String nome,
            @Param("matricula") String matricula,
            @Param("cpf") String cpf,
            @Param("email") String email,
            @Param("telefone") String telefone,
            @Param("unidadeOrganizacionalId") Long unidadeOrganizacionalId,
            @Param("perfilId") Long perfilId,
            Pageable pageable);

    Optional<Usuario> findByEmail(String email);

    Boolean existsByEmail(String Email);
}