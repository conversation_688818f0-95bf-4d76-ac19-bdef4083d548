package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UnidadeOrganizacionalRepository extends JpaRepository<UnidadeOrganizacional, Long> {
    boolean existsBySigla(String sigla);
    boolean existsByUnidadePaiId(Long unidadePaiId);
}